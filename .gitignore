# Dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# Build outputs
/dist
/build
/out
/.next
/.nuxt

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
/coverage
.nyc_output

# Temporary files
*.tmp
*.temp
.cache/

# System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Optional: Specific to your project
# Add any other files/folders specific to your project that shouldn't be tracked
