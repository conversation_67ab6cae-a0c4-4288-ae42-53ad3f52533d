// Simple test to verify redirect utilities work correctly

import { 
  getIntendedDestination, 
  saveIntendedDestination, 
  clearIntendedDestination,
  getLoginRedirectUrl 
} from '../redirectUtils';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock URLSearchParams
const mockSearchParams = {
  get: jest.fn(),
};

describe('redirectUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getIntendedDestination', () => {
    it('should return redirect param when valid', () => {
      mockSearchParams.get.mockReturnValue('/chat');
      const result = getIntendedDestination(mockSearchParams);
      expect(result).toBe('/chat');
    });

    it('should return saved destination when no redirect param', () => {
      mockSearchParams.get.mockReturnValue(null);
      localStorageMock.getItem.mockReturnValue('/profile');
      const result = getIntendedDestination(mockSearchParams);
      expect(result).toBe('/profile');
    });

    it('should return dashboard as default', () => {
      mockSearchParams.get.mockReturnValue(null);
      localStorageMock.getItem.mockReturnValue(null);
      const result = getIntendedDestination(mockSearchParams);
      expect(result).toBe('/dashboard');
    });

    it('should reject invalid paths', () => {
      mockSearchParams.get.mockReturnValue('/login');
      const result = getIntendedDestination(mockSearchParams);
      expect(result).toBe('/dashboard');
    });
  });

  describe('saveIntendedDestination', () => {
    it('should save valid paths', () => {
      saveIntendedDestination('/chat');
      expect(localStorageMock.setItem).toHaveBeenCalledWith('intendedDestination', '/chat');
    });

    it('should not save auth pages', () => {
      saveIntendedDestination('/login');
      expect(localStorageMock.setItem).not.toHaveBeenCalled();
    });
  });

  describe('getLoginRedirectUrl', () => {
    it('should create login URL with redirect param', () => {
      const result = getLoginRedirectUrl('/chat');
      expect(result).toBe('/login?redirect=%2Fchat');
    });

    it('should return plain login for invalid paths', () => {
      const result = getLoginRedirectUrl('/login');
      expect(result).toBe('/login');
    });
  });
});
