{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "socket.io": "^4.7.4", "validator": "^13.11.0"}}