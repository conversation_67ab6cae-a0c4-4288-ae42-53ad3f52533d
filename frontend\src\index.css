@import "tailwindcss";
@import "tw-animate-css";

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: rgba(255, 255, 255, 1);
  --foreground: rgba(37, 37, 37, 1);
  --card: rgba(255, 255, 255, 1);
  --card-foreground: rgba(37, 37, 37, 1);
  --popover: rgba(255, 255, 255, 1);
  --popover-foreground: rgba(37, 37, 37, 1);
  --primary: rgba(52, 52, 52, 1);
  --primary-foreground: rgba(251, 251, 251, 1);
  --secondary: rgba(247, 247, 247, 1);
  --secondary-foreground: rgba(52, 52, 52, 1);
  --muted: rgba(247, 247, 247, 1);
  --muted-foreground: rgba(142, 142, 142, 1);
  --accent: rgba(247, 247, 247, 1);
  --accent-foreground: rgba(52, 52, 52, 1);
  --destructive: rgba(239, 68, 68, 1);
  --border: rgb(219, 217, 217);
  --input: rgba(235, 235, 235, 1);
  --ring: rgba(180, 180, 180, 1);
  --chart-1: rgba(76, 175, 80, 1);
  --chart-2: rgba(33, 150, 243, 1);
  --chart-3: rgba(63, 81, 181, 1);
  --chart-4: rgba(255, 152, 0, 1);
  --chart-5: rgba(255, 87, 34, 1);
  --sidebar: rgba(251, 251, 251, 1);
  --sidebar-foreground: rgba(37, 37, 37, 1);
  --sidebar-primary: rgba(52, 52, 52, 1);
  --sidebar-primary-foreground: rgba(251, 251, 251, 1);
  --sidebar-accent: rgba(247, 247, 247, 1);
  --sidebar-accent-foreground: rgba(52, 52, 52, 1);
  --sidebar-border: rgba(235, 235, 235, 1);
  --sidebar-ring: rgba(180, 180, 180, 1);
}

.dark {
  --background: rgba(37, 37, 37, 1);
  --foreground: rgba(251, 251, 251, 1);
  --card: rgba(52, 52, 52, 1);
  --card-foreground: rgba(251, 251, 251, 1);
  --popover: rgba(52, 52, 52, 1);
  --popover-foreground: rgba(251, 251, 251, 1);
  --primary: rgba(235, 235, 235, 1);
  --primary-foreground: rgba(52, 52, 52, 1);
  --secondary: rgba(68, 68, 68, 1);
  --secondary-foreground: rgba(251, 251, 251, 1);
  --muted: rgba(68, 68, 68, 1);
  --muted-foreground: rgba(180, 180, 180, 1);
  --accent: rgba(68, 68, 68, 1);
  --accent-foreground: rgba(251, 251, 251, 1);
  --destructive: rgba(220, 38, 38, 1);
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.15);
  --ring: rgba(142, 142, 142, 1);
  --chart-1: rgba(124, 77, 255, 1);
  --chart-2: rgba(0, 176, 255, 1);
  --chart-3: rgba(255, 87, 34, 1);
  --chart-4: rgba(186, 104, 200, 1);
  --chart-5: rgba(255, 61, 0, 1);
  --sidebar: rgba(52, 52, 52, 1);
  --sidebar-foreground: rgba(251, 251, 251, 1);
  --sidebar-primary: rgba(124, 77, 255, 1);
  --sidebar-primary-foreground: rgba(251, 251, 251, 1);
  --sidebar-accent: rgba(68, 68, 68, 1);
  --sidebar-accent-foreground: rgba(251, 251, 251, 1);
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(142, 142, 142, 1);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
